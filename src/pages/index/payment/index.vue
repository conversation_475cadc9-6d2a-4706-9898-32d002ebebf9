<script setup>
import { showModal, showToast } from '@uni-helper/uni-promises'

// 响应式数据
const loading = ref(false)
const activeCategory = ref('all')
const timeFilter = ref('recent3months')

// 待缴费用数据
const pendingPayment = ref({
  count: 1,
  amount: 2650.00,
  description: '2024年12月租金+物业费',
  dueDate: '2024-12-10',
  isOverdue: false,
})

// 缴费记录数据
const paymentRecords = ref([
  {
    id: 'PAY20241201001',
    title: '2024年12月租金',
    amount: 2650.00,
    category: 'rent',
    status: 'pending',
    billNumber: 'PAY20241201001',
    dueDate: '2024-12-10',
    createTime: '2024-12-01 09:00:00',
  },
  {
    id: 'PAY20241105001',
    title: '2024年11月',
    amount: 135.80,
    category: 'utilities',
    status: 'paid',
    billNumber: 'PAY20241105001',
    paymentMethod: 'wechat',
    paidTime: '2024-11-05 14:30:00',
    createTime: '2024-11-01 09:00:00',
  },
  {
    id: 'PAY20240901001',
    title: '2024年9-11月租金',
    amount: 7950.00,
    category: 'rent',
    status: 'paid',
    billNumber: 'PAY20240901001',
    paymentMethod: 'wechat',
    paidTime: '2024-09-01 09:15:00',
    createTime: '2024-08-25 09:00:00',
  },
  {
    id: 'PAY20240901003',
    title: '租房押金',
    amount: 2500.00,
    category: 'deposit',
    status: 'paid',
    billNumber: 'PAY20240901003',
    paymentMethod: 'wechat',
    paidTime: '2024-09-01 09:18:00',
    createTime: '2024-08-25 09:00:00',
  },
])

// 分类筛选选项
const categoryOptions = computed(() => [
  { label: '全部费用', value: 'all' },
  { label: '租金', value: 'rent' },
  { label: '物业费', value: 'property' },
  { label: '水电费', value: 'utilities' },
  { label: '押金', value: 'deposit' },
])

// 过滤后的记录
const filteredRecords = computed(() => {
  if (activeCategory.value === 'all') {
    return paymentRecords.value
  }
  return paymentRecords.value.filter(record => record.category === activeCategory.value)
})

// 时间筛选选项
const timeFilterOptions = [
  { label: '近三个月', value: 'recent3months' },
  { label: '近半年', value: 'recent6months' },
  { label: '近一年', value: 'recentyear' },
]

// 缴费说明数据
const paymentNotices = [
  {
    type: 'info',
    text: '租金按季度收取，每期到期前5天内完成缴费',
  },
  {
    type: 'success',
    text: '水电费每月5日生成账单，可随时缴纳',
  },
  {
    type: 'warning',
    text: '逾期未缴纳将收取日息0.5%的滞纳金，逾期30天将被清退',
  },
  {
    type: 'success',
    text: '缴费成功后可在"查看收据"中获取电子收据，如需纸质收据请联系前台',
  },
]

// 生命周期
onMounted(() => {
  loadPaymentData()
})

// 方法
async function loadPaymentData() {
  try {
    loading.value = true
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  catch (error) {
    console.error('加载缴费数据失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none',
    })
  }
  finally {
    loading.value = false
  }
}

// 分类切换
function handleCategoryChange({ value }) {
  activeCategory.value = value
  uni.vibrateShort?.()
}

// 立即缴费
async function handlePayNow(payment) {
  try {
    const amount = payment?.amount || pendingPayment.value.amount
    const result = await showModal({
      title: '确认缴费',
      content: `确定要缴纳 ¥${amount.toFixed(2)} 的费用吗？`,
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消',
    })

    if (result.confirm) {
      await showToast({
        title: '正在跳转支付...',
        icon: 'loading',
      })

      // 模拟支付流程
      setTimeout(() => {
        uni.showToast({
          title: '支付功能开发中',
          icon: 'none',
        })
      }, 1500)
    }
  }
  catch (error) {
    console.error('支付失败:', error)
    uni.showToast({
      title: '支付失败，请重试',
      icon: 'none',
    })
  }
}

// 查看收据
function handleViewReceipt(record) {
  console.log('查看收据:', record)
  uni.showToast({
    title: '查看收据功能开发中',
    icon: 'none',
  })
}

// 查询收款码安全
function handleSecurityCheck() {
  uni.showToast({
    title: '查询收款码安全功能开发中',
    icon: 'none',
  })
}

// 时间筛选
function handleTimeFilterChange({ value }) {
  timeFilter.value = value
  const selected = timeFilterOptions.find(item => item.value === value)
  uni.showToast({
    title: `已切换到${selected.label}`,
    icon: 'none',
  })
}

// 获取状态样式
function getStatusStyle(status) {
  switch (status) {
    case 'pending':
      return {
        bgClass: 'bg-orange-100',
        textClass: 'text-orange-600',
        dotClass: 'bg-orange-500',
        label: '待缴费',
      }
    case 'paid':
      return {
        bgClass: 'bg-green-100',
        textClass: 'text-green-600',
        dotClass: 'bg-green-500',
        label: '已缴费',
      }
    default:
      return {
        bgClass: 'bg-gray-100',
        textClass: 'text-gray-600',
        dotClass: 'bg-gray-500',
        label: '未知',
      }
  }
}

// 获取支付方式文本
function getPaymentMethodText(method) {
  switch (method) {
    case 'wechat':
      return '微信支付'
    case 'alipay':
      return '支付宝'
    case 'bank':
      return '银行转账'
    default:
      return ''
  }
}

// 获取缴费说明图标样式
function getNoticeIconStyle(type) {
  switch (type) {
    case 'info':
      return 'bg-blue-500'
    case 'success':
      return 'bg-green-500'
    case 'warning':
      return 'bg-orange-500'
    case 'error':
      return 'bg-red-500'
    default:
      return 'bg-gray-500'
  }
}
</script>

<template>
  <view class="h-full px-4">
    <!-- 待缴费用卡片 -->
    <view class="my-4">
      <view class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 text-white shadow-lg">
        <!-- 顶部标题行 -->
        <view class="flex justify-between items-center">
          <view class="flex items-center">
            <text class="text-lg font-medium">
              您有{{ pendingPayment.count }}笔待缴费用
            </text>
          </view>
          <view class="text-xs opacity-90 bg-white/20 px-3 py-1 rounded-full" @click="handleSecurityCheck">
            逾期将收取滞纳金
          </view>
        </view>

        <!-- 费用描述 -->
        <view class="text-sm opacity-90 mb-1 border-b border-white/15 pb-4 pt-1">
          请及时缴纳以下费用
        </view>

        <!-- 费用详情 -->
        <view class="mt-4 mb-6">
          <view class="text-base mb-2">
            {{ pendingPayment.description }}
          </view>
          <view class="flex justify-between items-center">
            <view class="text-sm opacity-90">
              到期日期：{{ pendingPayment.dueDate }}
            </view>
            <view class="text-2xl font-bold">
              ¥{{ pendingPayment.amount.toFixed(2) }}
            </view>
          </view>
        </view>

        <!-- 立即缴纳按钮 -->
        <wd-button
          type="info"
          size="large"
          block
          custom-class="!bg-white !text-blue-600 !border-white font-medium"
          @click="handlePayNow()"
        >
          立即缴纳
        </wd-button>
      </view>
    </view>

    <!-- 筛选菜单和记录列表 -->
    <view class="">
      <!-- 筛选菜单 -->
      <view class="bg-white overflow-hidden rounded-md shadow-sm">
        <wd-drop-menu>
          <wd-drop-menu-item
            v-model="activeCategory"
            :options="categoryOptions"
            @change="handleCategoryChange"
          />
          <wd-drop-menu-item
            v-model="timeFilter"
            :options="timeFilterOptions"
            @change="handleTimeFilterChange"
          />
        </wd-drop-menu>
      </view>

      <!-- 记录列表 -->
      <view class="mt-4">
        <view
          v-for="record of filteredRecords"
          :key="record.id"
          class="bg-white rounded-xl mb-3 p-4 shadow-sm"
        >
          <view class="flex items-start justify-between">
            <!-- 左侧内容 -->
            <view class="flex-1">
              <!-- 状态标签和标题 -->
              <view class="flex items-center mb-2">
                <view
                  class="px-2 py-1 rounded-full text-xs mr-3" :class="[
                    getStatusStyle(record.status).bgClass,
                    getStatusStyle(record.status).textClass,
                  ]"
                >
                  {{ getStatusStyle(record.status).label }}
                </view>
                <text class="text-base font-medium text-gray-800">
                  {{ record.title }}
                </text>
              </view>

              <!-- 缴费单号 -->
              <view class="text-sm text-gray-500 mb-1">
                缴费单号：{{ record.billNumber }}
              </view>

              <!-- 支付方式和时间 -->
              <view class="text-sm text-gray-500">
                <text v-if="record.status === 'paid' && record.paymentMethod">
                  支付方式：{{ getPaymentMethodText(record.paymentMethod) }}
                </text>
              </view>

              <!-- 时间信息 -->
              <view class="text-xs text-gray-400 mt-2">
                <text v-if="record.status === 'paid' && record.paidTime">
                  {{ record.paidTime }}
                </text>
                <text v-else-if="record.dueDate">
                  到期时间：{{ record.dueDate }}
                </text>
              </view>
            </view>

            <!-- 右侧金额和操作 -->
            <view class="flex flex-col items-end">
              <view class="text-lg font-bold text-gray-800 mb-2">
                ¥{{ record.amount.toFixed(2) }}
              </view>
              <wd-button
                v-if="record.status === 'paid'"
                type="primary"
                size="small"
                plain
                @click="handleViewReceipt(record)"
              >
                查看收据
              </wd-button>
            </view>
          </view>
        </view>

        <!-- 加载更多提示 -->
        <view v-if="filteredRecords.length === 0" class="text-center py-8 text-gray-500">
          暂无{{ activeCategory === 'all' ? '' : categoryOptions.find(c => c.value === activeCategory)?.label }}记录
        </view>
      </view>
    </view>

    <!-- 缴费说明 -->
    <view class="bg-white mb-4 rounded-xl p-4 shadow-sm">
      <view class="flex items-center mb-3">
        <view class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-2">
          <text class="text-white text-xs">
            !
          </text>
        </view>
        <text class="text-base font-medium text-gray-800">
          缴费说明
        </text>
      </view>

      <view class="space-y-2">
        <view
          v-for="(notice, index) in paymentNotices"
          :key="index"
          class="flex items-start"
        >
          <view
            class="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0" :class="[
              getNoticeIconStyle(notice.type),
            ]"
          ></view>
          <text class="text-sm text-gray-600 leading-relaxed">
            {{ notice.text }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
</style>
